using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomPropertyDrawer(typeof(PhraseData))]
public class PhraseDataDrawer : PropertyDrawer
{
    private static Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
    private static Dictionary<string, bool> _sentencesFoldout = new Dictionary<string, bool>();
    
    private readonly Color _phraseHeaderColor = new Color(0.4f, 0.8f, 0.4f, 0.3f);
    private readonly Color _sentenceAreaColor = new Color(0.9f, 0.9f, 0.9f, 0.1f);
    
    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        string key = GetPropertyKey(property);
        bool isExpanded = _foldoutStates.ContainsKey(key) ? _foldoutStates[key] : false;
        
        if (!isExpanded)
            return EditorGUIUtility.singleLineHeight + 4;
            
        float height = EditorGUIUtility.singleLineHeight * 6; // Header + basic properties
        
        // Add height for sentences if expanded
        bool sentencesExpanded = _sentencesFoldout.ContainsKey(key) ? _sentencesFoldout[key] : true;
        if (sentencesExpanded)
        {
            SerializedProperty sentencesProp = property.FindPropertyRelative("Sentences");
            if (sentencesProp != null)
            {
                height += EditorGUI.GetPropertyHeight(sentencesProp, true) + 10;
            }
        }
        else
        {
            height += EditorGUIUtility.singleLineHeight + 5; // Just the foldout line
        }
        
        return height + 15; // Extra padding
    }
    
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);
        
        string key = GetPropertyKey(property);
        bool isExpanded = _foldoutStates.ContainsKey(key) ? _foldoutStates[key] : false;
        
        // Get phrase info for preview
        SerializedProperty sentencesProp = property.FindPropertyRelative("Sentences");
        int sentenceCount = sentencesProp != null ? sentencesProp.arraySize : 0;
        
        // Create preview text from first sentence
        string previewText = "[Empty Phrase]";
        if (sentenceCount > 0)
        {
            SerializedProperty firstSentence = sentencesProp.GetArrayElementAtIndex(0);
            SerializedProperty textProp = firstSentence.FindPropertyRelative("Text");
            if (textProp != null && !string.IsNullOrEmpty(textProp.stringValue))
            {
                previewText = textProp.stringValue;
                if (previewText.Length > 50)
                    previewText = previewText.Substring(0, 50) + "...";
            }
        }
        
        // Header with colored background
        Rect headerRect = new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight + 2);
        EditorGUI.DrawRect(headerRect, _phraseHeaderColor);
        
        // Foldout with phrase info
        string foldoutLabel = $"📄 Phrase ({sentenceCount} sentences): {previewText}";
        Rect foldoutRect = new Rect(position.x + 2, position.y + 1, position.width - 4, EditorGUIUtility.singleLineHeight);
        isExpanded = EditorGUI.Foldout(foldoutRect, isExpanded, foldoutLabel, true, EditorStyles.foldoutHeader);
        _foldoutStates[key] = isExpanded;
        
        if (!isExpanded)
        {
            EditorGUI.EndProperty();
            return;
        }
        
        // Content area
        float yOffset = EditorGUIUtility.singleLineHeight + 4;
        Rect contentRect = new Rect(position.x, position.y + yOffset, position.width, position.height - yOffset);
        
        EditorGUI.indentLevel++;
        DrawExpandedContent(contentRect, property, key);
        EditorGUI.indentLevel--;
        
        EditorGUI.EndProperty();
    }
    
    private void DrawExpandedContent(Rect contentRect, SerializedProperty property, string key)
    {
        float yOffset = 0;
        float lineHeight = EditorGUIUtility.singleLineHeight;
        
        // Phrase settings section
        EditorGUI.LabelField(new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight), 
                           "⚙️ Phrase Settings", EditorStyles.boldLabel);
        yOffset += lineHeight + 2;
        
        // Original text (editable)
        SerializedProperty originalTextProp = property.FindPropertyRelative("OriginalText");
        if (originalTextProp != null)
        {
            Rect textRect = new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight * 2);
            EditorGUI.LabelField(new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight), "📝 Original Text:");
            yOffset += lineHeight;
            
            textRect = new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight * 1.5f);
            originalTextProp.stringValue = EditorGUI.TextArea(textRect, originalTextProp.stringValue);
            yOffset += lineHeight * 1.5f + 5;
        }
        
        // Delay after phrase
        DrawProperty(contentRect, property, "DelayAfterPhrase", "⏳ Delay After Phrase", ref yOffset);
        
        // Control settings
        DrawProperty(contentRect, property, "RequireSkipToAdvance", "⏭️ Require Skip To Advance", ref yOffset);
        DrawProperty(contentRect, property, "WaitForEvent", "⏳ Wait For Event", ref yOffset);
        
        yOffset += 5;
        
        // Sentences section
        bool sentencesExpanded = _sentencesFoldout.ContainsKey(key) ? _sentencesFoldout[key] : true;
        
        Rect sentenceHeaderRect = new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight);
        EditorGUI.DrawRect(sentenceHeaderRect, _sentenceAreaColor);
        
        SerializedProperty sentencesProp = property.FindPropertyRelative("Sentences");
        int sentenceCount = sentencesProp != null ? sentencesProp.arraySize : 0;
        
        string sentencesLabel = $"📝 Sentences ({sentenceCount})";
        sentencesExpanded = EditorGUI.Foldout(sentenceHeaderRect, sentencesExpanded, sentencesLabel, true, EditorStyles.foldoutHeader);
        _sentencesFoldout[key] = sentencesExpanded;
        yOffset += lineHeight + 2;
        
        if (sentencesExpanded && sentencesProp != null)
        {
            // Sentences list with custom styling
            Rect sentencesRect = new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, 
                                        EditorGUI.GetPropertyHeight(sentencesProp, true));
            
            // Background for sentences area
            EditorGUI.DrawRect(new Rect(sentencesRect.x - 2, sentencesRect.y - 2, 
                                      sentencesRect.width + 4, sentencesRect.height + 4), _sentenceAreaColor);
            
            EditorGUI.PropertyField(sentencesRect, sentencesProp, true);
            yOffset += sentencesRect.height + 5;
            
            // Quick actions for sentences
            DrawSentenceQuickActions(new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight), 
                                   sentencesProp);
        }
    }
    
    private void DrawSentenceQuickActions(Rect rect, SerializedProperty sentencesProp)
    {
        float buttonWidth = rect.width / 3f - 2f;
        
        // Add sentence button
        if (GUI.Button(new Rect(rect.x, rect.y, buttonWidth, rect.height), "➕ Add Sentence"))
        {
            sentencesProp.arraySize++;
            SerializedProperty newSentence = sentencesProp.GetArrayElementAtIndex(sentencesProp.arraySize - 1);
            
            // Initialize new sentence with default values
            SerializedProperty textProp = newSentence.FindPropertyRelative("Text");
            if (textProp != null) textProp.stringValue = "New sentence...";
            
            SerializedProperty idProp = newSentence.FindPropertyRelative("SentenceID");
            if (idProp != null) idProp.stringValue = $"sentence_{sentencesProp.arraySize}";
        }
        
        // Clear all sentences button
        if (GUI.Button(new Rect(rect.x + buttonWidth + 2, rect.y, buttonWidth, rect.height), "🗑️ Clear All"))
        {
            if (EditorUtility.DisplayDialog("Clear All Sentences", 
                "Are you sure you want to remove all sentences from this phrase?", "Yes", "Cancel"))
            {
                sentencesProp.arraySize = 0;
            }
        }
        
        // Reparse button (if document reference available)
        if (GUI.Button(new Rect(rect.x + (buttonWidth + 2) * 2, rect.y, buttonWidth, rect.height), "🔄 Reparse"))
        {
            // Try to find the document and reparse this phrase
            Object targetObject = sentencesProp.serializedObject.targetObject;
            if (targetObject is VisualTextDocument document)
            {
                // Find which phrase this is and reparse it
                for (int i = 0; i < document.Phrases.Count; i++)
                {
                    // This is a simplified approach - in a real implementation,
                    // you'd want to properly identify which phrase this property represents
                    document.ParseAndConfigureSinglePhrase(document.Phrases[i]);
                }
                EditorUtility.SetDirty(document);
            }
        }
    }
    
    private void DrawProperty(Rect contentRect, SerializedProperty property, string propertyName, string label, ref float yOffset)
    {
        float lineHeight = EditorGUIUtility.singleLineHeight;
        Rect propRect = new Rect(contentRect.x, contentRect.y + yOffset, contentRect.width, lineHeight);
        
        SerializedProperty prop = property.FindPropertyRelative(propertyName);
        if (prop != null)
        {
            EditorGUI.PropertyField(propRect, prop, new GUIContent(label));
            yOffset += lineHeight + 2;
        }
    }
    
    private string GetPropertyKey(SerializedProperty property)
    {
        return $"{property.serializedObject.targetObject.GetInstanceID()}_{property.propertyPath}";
    }
}
