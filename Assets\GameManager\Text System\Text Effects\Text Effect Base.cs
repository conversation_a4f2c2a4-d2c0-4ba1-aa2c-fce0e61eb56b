using System.Collections;
using TMPro;
using UnityEngine;
using UnityEditor;
using Unity.EditorCoroutines.Editor;

/// <summary>
/// Categorias de efeitos de texto baseadas no comportamento
/// </summary>
public enum TextEffectCategory
{
    [Tooltip("Efeitos que rodam continuamente em segundo plano")]
    Continuous,

    [Tooltip("Efeitos de escrita/typewriter que revelam texto")]
    Writing,

    [Tooltip("Efeitos de delete que removem texto")]
    Deleting,

    [Tooltip("Efeitos que executam uma vez e param")]
    OneShot,

    [Tooltip("Efeitos que respondem a interação do usuário")]
    Interactive
}

/// <summary>
/// Classe base para todos os efeitos de texto. Permite uma estrutura extensível para
/// animações, sons, e outros comportamentos de texto.
/// </summary>
public abstract class TextEffectBase : InstantiableSO
{
    [Header("Categoria do Efeito")]
    [Tooltip("Categoria que define o comportamento do efeito")]
    public TextEffectCategory Category = TextEffectCategory.Continuous;

    [Tooltip("Se verdadeiro, este efeito será executado continuamente em segundo plano, sem pausar a progressão do texto.")]
    public virtual bool IsContinuous { get { return Category == TextEffectCategory.Continuous; } }

    /// <summary>
    /// Inicia a execução do efeito.
    /// </summary>
    public virtual IEnumerator StartEffect(VisualTextDisplayer displayer, SentenceDisplayData sentence, TextMeshProUGUI textComponent, int start, int end)
    {
        if (!Application.isPlaying){
            EditorUtility.SetDirty(textComponent);
        }
        yield return null;
    }

    public virtual IEnumerator StartEffect(VisualTextDisplayer displayer, SentenceDisplayData sentence, TextMeshProUGUI textComponent, int start, int end, SentenceInfo animData)
    {
        yield break;
    }

    /// <summary>
    /// Sinaliza ao efeito para parar sua execução.
    /// </summary>
    public virtual void StopEffect() { }

    /// <summary>
    /// Reseta o estado do efeito. Chamado após a conclusão ou interrupção.
    /// </summary>
    public virtual void Reset(TextMeshProUGUI textComponent) {}
    public virtual void Reset(TextMeshProUGUI textComponent, SentenceInfo animData) {}
}
