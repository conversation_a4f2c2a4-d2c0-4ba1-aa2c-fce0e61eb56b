using UnityEngine;
using UnityEditor;
using TMPro;
using System.Collections.Generic;
using Unity.EditorCoroutines.Editor;
using System.Linq;
using UnityEngine.UI;

public class VisualTextDocumentEditorWindow : EditorWindow
{
    private VisualTextDocument _document;
    private SerializedObject _serializedDocument;

    // Preview Objects
    private GameObject _previewObject;
    private VisualTextDisplayer _previewDisplayer;
    private TextMeshProUGUI _previewTextComponent;

    // Preview State
    private Vector2 _previewScrollPosition;
    private bool _showPreviewInWindow = true;
    private bool _showPreviewControls = true;
    private bool _showPreviewSettings = false;
    private bool _autoPreview = false;
    private float _previewSpeed = 1f;
    private int _currentPreviewPhrase = 0;
    private int _currentPreviewSentence = 0;

    // UI State
    private Vector2 _scrollPosition;
    private Vector2 _defaultsScrollPosition;
    private Dictionary<PhraseData, bool> _phraseFoldouts = new Dictionary<PhraseData, bool>();
    private int _toolbarIndex = 0;
    private string[] _toolbarStrings = { "📝 Editor", "👁️ Preview", "⚙️ Settings", "📊 Statistics" };

    // Editor State
    private bool _showDefaults = false;
    private bool _autoSave = true;
    private string _searchFilter = "";
    private int _selectedPhraseIndex = -1;

    // Colors for UI
    private readonly Color _headerColor = new Color(0.2f, 0.4f, 0.8f, 0.3f);
    private readonly Color _successColor = new Color(0.2f, 0.8f, 0.2f, 0.3f);
    private readonly Color _warningColor = new Color(0.8f, 0.6f, 0.2f, 0.3f);
    private readonly Color _errorColor = new Color(0.8f, 0.2f, 0.2f, 0.3f);

    [MenuItem("Tools/Text System/Visual Text Document Editor")]
    public static void ShowWindow()
    {
        GetWindow<VisualTextDocumentEditorWindow>("Visual Text Editor");
    }

    public static void ShowWindow(VisualTextDocument document)
    {
        VisualTextDocumentEditorWindow window = GetWindow<VisualTextDocumentEditorWindow>("Visual Text Editor");
        window.SetDocument(document);
    }

    private void SetDocument(VisualTextDocument document)
    {
        _document = document;
        if (_document != null)
        {
            _serializedDocument = new SerializedObject(_document);
            _phraseFoldouts.Clear(); // Reset foldouts for the new document
        }
        else
        {
            _serializedDocument = null;
        }
    }

    private void OnEnable()
    {
        CreatePreviewObject();
        // If a document was selected in the project, open the window with it
        if (_document == null && Selection.activeObject is VisualTextDocument selectedDoc)
        {
            SetDocument(selectedDoc);
        }

        // Auto-refresh a cada segundo se auto preview estiver ativo
        EditorApplication.update += OnEditorUpdate;
    }

    private void OnDisable()
    {
        EditorApplication.update -= OnEditorUpdate;
        DestroyPreviewObject();
    }

    private string _lastFullText = "";
    private void OnEditorUpdate()
    {
        // Auto-refresh do preview se o texto mudou
        if (_autoPreview && _document != null && _document.fullText != _lastFullText)
        {
            _lastFullText = _document.fullText;
            if (_previewDisplayer != null && _previewDisplayer.IsDisplaying)
            {
                // Re-parse e restart preview
                _document.ParseAndConfigureText(_document.fullText);
                _previewDisplayer.DisplayFromEditor(_document);
            }
        }
    }

    private void OnGUI()
    {
        DrawHeader();

        if (_document == null)
        {
            DrawNoDocumentState();
            return;
        }

        _serializedDocument.Update();

        DrawToolbar();

        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

        switch (_toolbarIndex)
        {
            case 0: DrawEditorTab(); break;
            case 1: DrawPreviewTab(); break;
            case 2: DrawSettingsTab(); break;
            case 3: DrawStatisticsTab(); break;
        }

        EditorGUILayout.EndScrollView();

        if (GUI.changed && _autoSave)
        {
            _serializedDocument.ApplyModifiedProperties();
            EditorUtility.SetDirty(_document);
        }

        DrawFooter();
    }

    private void DrawHeader()
    {
        // Header with colored background
        Rect headerRect = EditorGUILayout.GetControlRect(false, 60);
        EditorGUI.DrawRect(headerRect, _headerColor);

        GUILayout.BeginArea(headerRect);
        GUILayout.Space(5);

        GUILayout.BeginHorizontal();
        GUILayout.Label("📝 Visual Text Document Editor", EditorStyles.largeLabel);
        GUILayout.FlexibleSpace();

        // Auto-save toggle
        _autoSave = GUILayout.Toggle(_autoSave, "Auto Save", GUILayout.Width(80));

        // Manual save button
        EditorGUI.BeginDisabledGroup(_document == null);
        {
            if (_document != null)
            {
                _serializedDocument.ApplyModifiedProperties();
                EditorUtility.SetDirty(_document);
                AssetDatabase.SaveAssets();
            }
        }
        EditorGUI.EndDisabledGroup();
        GUILayout.EndHorizontal();

        GUILayout.Space(5);

        // Document selection
        GUILayout.BeginHorizontal();
        GUILayout.Label("Document:", GUILayout.Width(70));
        VisualTextDocument newDocument = (VisualTextDocument)EditorGUILayout.ObjectField(_document, typeof(VisualTextDocument), false);
        if (newDocument != _document)
        {
            SetDocument(newDocument);
        }

        // Quick create button
        if (GUILayout.Button("➕ New", GUILayout.Width(50)))
        {
            CreateNewDocument();
        }
        GUILayout.EndHorizontal();

        GUILayout.EndArea();
    }

    private void DrawNoDocumentState()
    {
        GUILayout.FlexibleSpace();

        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();

        GUILayout.BeginVertical(GUILayout.Width(300));
        EditorGUILayout.HelpBox("📄 No document selected\n\nSelect an existing VisualTextDocument or create a new one to start editing.", MessageType.Info);

        GUILayout.Space(10);

        if (GUILayout.Button("🆕 Create New Document", GUILayout.Height(30)))
        {
            CreateNewDocument();
        }

        if (GUILayout.Button("📂 Select Existing Document", GUILayout.Height(30)))
        {
            string path = EditorUtility.OpenFilePanel("Select Visual Text Document", "Assets", "asset");
            if (!string.IsNullOrEmpty(path))
            {
                path = "Assets" + path.Substring(Application.dataPath.Length);
                VisualTextDocument doc = AssetDatabase.LoadAssetAtPath<VisualTextDocument>(path);
                if (doc != null) SetDocument(doc);
            }
        }

        GUILayout.EndVertical();
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();

        GUILayout.FlexibleSpace();
    }

    private void DrawToolbar()
    {
        GUILayout.Space(5);
        _toolbarIndex = GUILayout.Toolbar(_toolbarIndex, _toolbarStrings, GUILayout.Height(25));
        GUILayout.Space(5);
    }

    private void DrawFooter()
    {
        if (_document == null) return;

        // Footer with document info
        Rect footerRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(footerRect, _headerColor);

        GUILayout.BeginArea(footerRect);
        GUILayout.BeginHorizontal();

        GUILayout.Label($"📊 {_document.Phrases.Count} phrases, {GetTotalSentenceCount()} sentences", EditorStyles.miniLabel);
        GUILayout.FlexibleSpace();

        if (_document != null)
        {
            GUILayout.Label($"📁 {AssetDatabase.GetAssetPath(_document)}", EditorStyles.miniLabel);
        }

        GUILayout.EndHorizontal();
        GUILayout.EndArea();
    }

    private void DrawEditorTab()
    {
        // Search and filter section
        DrawSearchSection();

        // Full Text Area with improved UI
        DrawFullTextSection();

        // Quick actions
        DrawQuickActions();

        // Default settings (collapsible)
        DrawDefaultSettings();

        // Phrases section with improved navigation
        DrawPhrasesSection();
    }

    private void DrawSearchSection()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("🔍", GUILayout.Width(20));
        _searchFilter = EditorGUILayout.TextField(_searchFilter, EditorStyles.toolbarSearchField);

        if (GUILayout.Button("Clear", EditorStyles.toolbarButton, GUILayout.Width(50)))
        {
            _searchFilter = "";
        }
        GUILayout.EndHorizontal();

        if (!string.IsNullOrEmpty(_searchFilter))
        {
            EditorGUILayout.HelpBox($"🔍 Filtering phrases containing: '{_searchFilter}'", MessageType.Info);
        }

        GUILayout.Space(5);
    }

    private void DrawFullTextSection()
    {
        // Header with background
        Rect headerRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(headerRect, _headerColor);
        EditorGUI.LabelField(headerRect, "📝 Full Raw Text", EditorStyles.boldLabel);

        SerializedProperty fullTextProp = _serializedDocument.FindProperty("fullText");

        // Text area with word count
        string currentText = fullTextProp.stringValue;
        int wordCount = string.IsNullOrEmpty(currentText) ? 0 : currentText.Split(' ', System.StringSplitOptions.RemoveEmptyEntries).Length;

        EditorGUILayout.LabelField($"Words: {wordCount} | Characters: {currentText.Length}", EditorStyles.miniLabel);
        fullTextProp.stringValue = EditorGUILayout.TextArea(fullTextProp.stringValue, GUILayout.Height(120));

        GUILayout.Space(5);
    }

    private void DrawQuickActions()
    {
        GUILayout.BeginHorizontal();

        // Parse button with status
        Color originalColor = GUI.backgroundColor;
        GUI.backgroundColor = _successColor;
        if (GUILayout.Button("🔄 Parse Full Text", GUILayout.Height(25)))
        {
            Undo.RecordObject(_document, "Parse Full Text");
            _document.ParseAndConfigureText(_document.fullText);
            EditorUtility.SetDirty(_document);
            _phraseFoldouts.Clear();
        }
        GUI.backgroundColor = originalColor;

        // Clear phrases button
        GUI.backgroundColor = _warningColor;
        if (GUILayout.Button("🗑️ Clear Phrases", GUILayout.Height(25)))
        {
            if (EditorUtility.DisplayDialog("Clear All Phrases",
                "Are you sure you want to remove all parsed phrases?", "Yes", "Cancel"))
            {
                Undo.RecordObject(_document, "Clear Phrases");
                _document.Phrases.Clear();
                EditorUtility.SetDirty(_document);
                _phraseFoldouts.Clear();
            }
        }
        GUI.backgroundColor = originalColor;

        // Add phrase button
        if (GUILayout.Button("➕ Add Phrase", GUILayout.Height(25)))
        {
            Undo.RecordObject(_document, "Add Phrase");
            var newPhrase = new PhraseData();
            newPhrase.OriginalText = "New phrase...";
            _document.Phrases.Add(newPhrase);
            EditorUtility.SetDirty(_document);
        }

        GUILayout.EndHorizontal();
        GUILayout.Space(10);
    }

    private void DrawDefaultSettings()
    {
        Rect headerRect = EditorGUILayout.GetControlRect(false, 20);
        EditorGUI.DrawRect(headerRect, _headerColor);

        _showDefaults = EditorGUI.Foldout(headerRect, _showDefaults, "⚙️ Default Settings", true, EditorStyles.foldoutHeader);

        if (_showDefaults)
        {
            EditorGUI.indentLevel++;

            _defaultsScrollPosition = EditorGUILayout.BeginScrollView(_defaultsScrollPosition, GUILayout.Height(200));

            EditorGUILayout.LabelField("Visual Defaults", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultTextColor"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultFontAsset"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultFontSize"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultIsBold"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultIsItalic"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultAlignment"));

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Timing Defaults", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultDelayAfterSentence"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultDelayAfterPhrase"));

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Typewriter Defaults", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultUseTypewriter"));
            if(_document.DefaultUseTypewriter)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultDelayPerCharacter"));
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultPauseOnPunctuation"));
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultPunctuationDelay"));
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.EndScrollView();
            EditorGUI.indentLevel--;
        }

        GUILayout.Space(10);
    }

    private void DrawPhrasesSection()
    {
        // Phrases header with count
        Rect phrasesHeaderRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(phrasesHeaderRect, _successColor);
        EditorGUI.LabelField(phrasesHeaderRect, $"📄 Phrases ({_document.Phrases.Count})", EditorStyles.boldLabel);

        if (_document.Phrases.Count == 0)
        {
            EditorGUILayout.HelpBox("No phrases found. Parse the full text above to generate phrases.", MessageType.Info);
            return;
        }

        // Phrases list
        SerializedProperty phrasesProp = _serializedDocument.FindProperty("Phrases");

        for (int i = 0; i < _document.Phrases.Count; i++)
        {
            PhraseData phrase = _document.Phrases[i];

            // Apply search filter
            if (!string.IsNullOrEmpty(_searchFilter))
            {
                bool matchFound = false;
                if (phrase.FullPhraseText != null && phrase.FullPhraseText.ToLower().Contains(_searchFilter.ToLower()))
                    matchFound = true;

                foreach (var sentence in phrase.Sentences)
                {
                    if (sentence.Text != null && sentence.Text.ToLower().Contains(_searchFilter.ToLower()))
                    {
                        matchFound = true;
                        break;
                    }
                }

                if (!matchFound) continue;
            }

            SerializedProperty phraseProp = phrasesProp.GetArrayElementAtIndex(i);
            EditorGUILayout.PropertyField(phraseProp, new GUIContent($"Phrase {i + 1}"), true);

            GUILayout.Space(5);
        }
    }

    private void DrawPreviewTab()
    {
        if (_document == null)
        {
            EditorGUILayout.HelpBox("Nenhum documento selecionado para preview.", MessageType.Warning);
            return;
        }

        _previewScrollPosition = EditorGUILayout.BeginScrollView(_previewScrollPosition);

        // Header com informações do documento
        DrawPreviewHeader();

        GUILayout.Space(10);

        // Preview em tempo real na janela
        if (_showPreviewInWindow)
        {
            DrawInWindowPreview();
            GUILayout.Space(10);
        }

        // Controles de preview
        if (_showPreviewControls)
        {
            DrawPreviewControls();
            GUILayout.Space(10);
        }

        // Configurações de preview
        if (_showPreviewSettings)
        {
            DrawPreviewSettings();
            GUILayout.Space(10);
        }

        // Navegação por frases/sentenças
        DrawPreviewNavigation();

        GUILayout.Space(10);

        // Informações de debug
        DrawPreviewDebugInfo();

        EditorGUILayout.EndScrollView();
    }

    private void DrawPreviewHeader()
    {
        EditorGUILayout.LabelField("👁️ Preview", EditorStyles.largeLabel);

        // Informações básicas do documento
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField($"📄 Frases: {_document.Phrases.Count}", GUILayout.Width(100));
        EditorGUILayout.LabelField($"📝 Sentenças: {GetTotalSentenceCount()}", GUILayout.Width(120));
        EditorGUILayout.LabelField($"⏱️ Tempo estimado: {GetEstimatedTime():F1}s", GUILayout.Width(150));
        EditorGUILayout.EndHorizontal();

        // Toggles para seções
        EditorGUILayout.BeginHorizontal();
        _showPreviewInWindow = EditorGUILayout.ToggleLeft("📺 Preview na Janela", _showPreviewInWindow, GUILayout.Width(150));
        _showPreviewControls = EditorGUILayout.ToggleLeft("🎮 Controles", _showPreviewControls, GUILayout.Width(100));
        _showPreviewSettings = EditorGUILayout.ToggleLeft("⚙️ Configurações", _showPreviewSettings, GUILayout.Width(120));
        EditorGUILayout.EndHorizontal();
    }

    private void DrawInWindowPreview()
    {
        EditorGUILayout.LabelField("📺 Preview em Tempo Real", EditorStyles.boldLabel);

        // Área de preview do texto
        var previewRect = GUILayoutUtility.GetRect(0, 150, GUILayout.ExpandWidth(true));
        EditorGUI.DrawRect(previewRect, new Color(0.1f, 0.1f, 0.1f, 0.8f));

        // Texto atual sendo exibido
        string currentText = GetCurrentPreviewText();
        if (!string.IsNullOrEmpty(currentText))
        {
            var textStyle = new GUIStyle(EditorStyles.label)
            {
                wordWrap = true,
                alignment = TextAnchor.MiddleCenter,
                fontSize = 14,
                normal = { textColor = Color.white }
            };

            var textRect = new Rect(previewRect.x + 10, previewRect.y + 10,
                                  previewRect.width - 20, previewRect.height - 20);
            EditorGUI.LabelField(textRect, currentText, textStyle);
        }
        else
        {
            var placeholderStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
            {
                fontSize = 12,
                normal = { textColor = Color.gray }
            };
            EditorGUI.LabelField(previewRect, "Clique em 'Preview Document' para ver o texto aqui", placeholderStyle);
        }

        // Barra de progresso
        if (_previewDisplayer != null && _previewDisplayer.IsDisplaying)
        {
            var progressRect = new Rect(previewRect.x, previewRect.yMax - 4, previewRect.width, 4);
            float progress = GetPreviewProgress();
            EditorGUI.DrawRect(progressRect, Color.gray);
            EditorGUI.DrawRect(new Rect(progressRect.x, progressRect.y, progressRect.width * progress, progressRect.height), Color.green);
        }
    }

    private void DrawPreviewControls()
    {
        EditorGUILayout.LabelField("🎮 Controles de Preview", EditorStyles.boldLabel);

        // Primeira linha de controles
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("▶️ Preview Document", GUILayout.Height(30)))
        {
            StartPreview();
        }

        if (GUILayout.Button("⏸️ Pausar", GUILayout.Height(30)))
        {
            PausePreview();
        }

        if (GUILayout.Button("⏹️ Parar", GUILayout.Height(30)))
        {
            StopPreview();
        }

        EditorGUILayout.EndHorizontal();

        // Segunda linha de controles
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("⏭️ Próxima Sentença"))
        {
            if (_previewDisplayer != null) _previewDisplayer.Skip();
        }

        if (GUILayout.Button("⏮️ Frase Anterior"))
        {
            NavigateToPreviousPhrase();
        }

        if (GUILayout.Button("⏭️ Próxima Frase"))
        {
            NavigateToNextPhrase();
        }

        EditorGUILayout.EndHorizontal();

        // Controle de velocidade
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("🏃 Velocidade:", GUILayout.Width(80));
        _previewSpeed = EditorGUILayout.Slider(_previewSpeed, 0.1f, 3f);
        if (GUILayout.Button("Reset", GUILayout.Width(50)))
        {
            _previewSpeed = 1f;
        }
        EditorGUILayout.EndHorizontal();

        // Auto preview
        _autoPreview = EditorGUILayout.ToggleLeft("🔄 Auto Preview (atualiza quando texto muda)", _autoPreview);
    }

    private void DrawPreviewSettings()
    {
        EditorGUILayout.LabelField("⚙️ Configurações de Preview", EditorStyles.boldLabel);

        if (_previewDisplayer == null)
        {
            EditorGUILayout.HelpBox("Preview object não encontrado. Reabra a janela.", MessageType.Error);
            return;
        }

        // Configurações do TextMeshPro
        EditorGUILayout.LabelField("📝 Configurações de Texto", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;

        if (_previewTextComponent != null)
        {
            _previewTextComponent.fontSize = EditorGUILayout.FloatField("Tamanho da Fonte", _previewTextComponent.fontSize);
            _previewTextComponent.color = EditorGUILayout.ColorField("Cor do Texto", _previewTextComponent.color);
            _previewTextComponent.alignment = (TextAlignmentOptions)EditorGUILayout.EnumPopup("Alinhamento", _previewTextComponent.alignment);

            var sizeDelta = _previewTextComponent.rectTransform.sizeDelta;
            sizeDelta = EditorGUILayout.Vector2Field("Tamanho da Área", sizeDelta);
            _previewTextComponent.rectTransform.sizeDelta = sizeDelta;
        }

        EditorGUI.indentLevel--;

        GUILayout.Space(5);

        // Configurações do Canvas
        EditorGUILayout.LabelField("🖼️ Configurações de Canvas", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;

        if (_previewObject != null)
        {
            var canvas = _previewObject.GetComponent<Canvas>();
            if (canvas != null)
            {
                canvas.renderMode = (RenderMode)EditorGUILayout.EnumPopup("Render Mode", canvas.renderMode);
                canvas.sortingOrder = EditorGUILayout.IntField("Sorting Order", canvas.sortingOrder);
            }
        }

        EditorGUI.indentLevel--;

        // Botões de utilidade
        GUILayout.Space(10);
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("🔄 Recriar Preview Object"))
        {
            CreatePreviewObject();
        }

        if (GUILayout.Button("📍 Focar na Scene"))
        {
            FocusPreviewInScene();
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawPreviewNavigation()
    {
        EditorGUILayout.LabelField("🧭 Navegação", EditorStyles.boldLabel);

        if (_document.Phrases.Count == 0)
        {
            EditorGUILayout.HelpBox("Nenhuma frase para navegar.", MessageType.Info);
            return;
        }

        // Seletor de frase
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Frase:", GUILayout.Width(50));
        int newPhraseIndex = EditorGUILayout.IntSlider(_currentPreviewPhrase, 0, _document.Phrases.Count - 1);
        if (newPhraseIndex != _currentPreviewPhrase)
        {
            _currentPreviewPhrase = newPhraseIndex;
            _currentPreviewSentence = 0; // Reset sentence when phrase changes
        }
        EditorGUILayout.EndHorizontal();

        // Seletor de sentença
        if (_currentPreviewPhrase < _document.Phrases.Count)
        {
            var currentPhrase = _document.Phrases[_currentPreviewPhrase];
            if (currentPhrase.Sentences.Count > 0)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Sentença:", GUILayout.Width(70));
                _currentPreviewSentence = EditorGUILayout.IntSlider(_currentPreviewSentence, 0, currentPhrase.Sentences.Count - 1);
                EditorGUILayout.EndHorizontal();

                // Preview da sentença atual
                var currentSentence = currentPhrase.Sentences[_currentPreviewSentence];
                EditorGUILayout.LabelField("Preview:", EditorStyles.boldLabel);
                EditorGUILayout.TextArea(currentSentence.Text, GUILayout.Height(40));

                // Informações da sentença
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"ID: {currentSentence.SentenceID ?? "N/A"}", GUILayout.Width(100));
                EditorGUILayout.LabelField($"Typewriter: {currentSentence.UseTypewriter}", GUILayout.Width(120));
                EditorGUILayout.LabelField($"Delay: {currentSentence.DelayPerCharacter:F3}s", GUILayout.Width(100));
                EditorGUILayout.EndHorizontal();
            }
        }
    }

    private void DrawPreviewDebugInfo()
    {
        EditorGUILayout.LabelField("🐛 Debug Info", EditorStyles.boldLabel);

        if (_previewDisplayer == null)
        {
            EditorGUILayout.LabelField("Preview Displayer: ❌ Null");
            return;
        }

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField($"Status: {(_previewDisplayer.IsDisplaying ? "▶️ Executando" : "⏹️ Parado")}", GUILayout.Width(150));
        EditorGUILayout.LabelField($"Object: {(_previewObject != null ? "✅ Ativo" : "❌ Null")}", GUILayout.Width(100));
        EditorGUILayout.EndHorizontal();

        if (_previewTextComponent != null)
        {
            EditorGUILayout.LabelField($"Texto atual: {Truncate(_previewTextComponent.text, 50)}");
        }
    }

    // Métodos auxiliares para preview
    private int GetTotalSentenceCount()
    {
        return _document?.Phrases?.Sum(p => p.Sentences?.Count ?? 0) ?? 0;
    }

    private float GetEstimatedTime()
    {
        if (_document?.Phrases == null) return 0f;

        float totalTime = 0f;
        foreach (var phrase in _document.Phrases)
        {
            if (phrase.Sentences != null)
            {
                foreach (var sentence in phrase.Sentences)
                {
                    if (sentence.UseTypewriter && !string.IsNullOrEmpty(sentence.Text))
                    {
                        totalTime += sentence.Text.Length * sentence.DelayPerCharacter;
                    }
                    totalTime += sentence.DelayAfterSentence;
                }
            }
            totalTime += phrase.DelayAfterPhrase;
        }

        return totalTime / _previewSpeed;
    }

    private string GetCurrentPreviewText()
    {
        if (_previewTextComponent != null && !string.IsNullOrEmpty(_previewTextComponent.text))
        {
            return _previewTextComponent.text;
        }

        // Fallback para navegação manual
        if (_document?.Phrases != null && _currentPreviewPhrase < _document.Phrases.Count)
        {
            var phrase = _document.Phrases[_currentPreviewPhrase];
            if (phrase.Sentences != null && _currentPreviewSentence < phrase.Sentences.Count)
            {
                return phrase.Sentences[_currentPreviewSentence].Text;
            }
        }

        return "";
    }

    private float GetPreviewProgress()
    {
        if (_document?.Phrases == null || _document.Phrases.Count == 0) return 0f;

        float totalSentences = GetTotalSentenceCount();
        if (totalSentences == 0) return 0f;

        float currentSentences = 0f;
        for (int i = 0; i < _currentPreviewPhrase && i < _document.Phrases.Count; i++)
        {
            currentSentences += _document.Phrases[i].Sentences?.Count ?? 0;
        }
        currentSentences += _currentPreviewSentence;

        return currentSentences / totalSentences;
    }

    private void StartPreview()
    {
        if (_document == null) return;

        if (_document.Phrases.Count == 0)
        {
            _document.ParseAndConfigureText(_document.fullText);
        }

        if (_previewDisplayer != null)
        {
            _previewDisplayer.DisplayFromEditor(_document);
        }
    }

    private void PausePreview()
    {
        // TODO: Implementar pause se o VisualTextDisplayer suportar
        if (_previewDisplayer != null)
        {
            _previewDisplayer.StopDisplay();
        }
    }

    private void StopPreview()
    {
        if (_previewDisplayer != null)
        {
            _previewDisplayer.StopDisplay();
        }
        _currentPreviewPhrase = 0;
        _currentPreviewSentence = 0;
    }

    private void NavigateToPreviousPhrase()
    {
        if (_currentPreviewPhrase > 0)
        {
            _currentPreviewPhrase--;
            _currentPreviewSentence = 0;
        }
    }

    private void NavigateToNextPhrase()
    {
        if (_currentPreviewPhrase < _document.Phrases.Count - 1)
        {
            _currentPreviewPhrase++;
            _currentPreviewSentence = 0;
        }
    }

    private void FocusPreviewInScene()
    {
        if (_previewObject != null)
        {
            Selection.activeGameObject = _previewObject;
            SceneView.FrameLastActiveSceneView();
        }
    }

    private void CreatePreviewObject()
    {
        // Clean up any old preview objects first
        DestroyPreviewObject();

        _previewObject = new GameObject("VTD_Preview");
        _previewObject.hideFlags = HideFlags.DontSave; // Não salva na cena

        // Setup Canvas
        var canvas = _previewObject.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 1000; // Sempre por cima

        // Setup CanvasScaler para responsividade
        var scaler = _previewObject.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        // Setup GraphicRaycaster
        _previewObject.AddComponent<GraphicRaycaster>();

        // Setup TextMeshPro
        _previewTextComponent = _previewObject.AddComponent<TextMeshProUGUI>();
        _previewTextComponent.fontSize = 36;
        _previewTextComponent.color = Color.white;
        _previewTextComponent.alignment = TextAlignmentOptions.Center;
        _previewTextComponent.rectTransform.sizeDelta = new Vector2(800, 200);
        _previewTextComponent.rectTransform.anchoredPosition = Vector2.zero;

        // Configurações avançadas do TextMeshPro
        _previewTextComponent.enableWordWrapping = true;
        _previewTextComponent.overflowMode = TextOverflowModes.Overflow;
        _previewTextComponent.richText = true;

        // Setup VisualTextDisplayer
        _previewDisplayer = _previewObject.AddComponent<VisualTextDisplayer>();
        _previewDisplayer.textComponent = _previewTextComponent;

        // Adiciona eventos para tracking
        if (_previewDisplayer != null)
        {
            _previewDisplayer.OnSentenceStarted += OnPreviewSentenceStarted;
            _previewDisplayer.OnSentenceFinished += OnPreviewSentenceFinished;
            _previewDisplayer.OnDisplayFinished += OnPreviewDisplayFinished;
        }

        Debug.Log("Preview object criado com sucesso!");
    }

    // Callbacks do preview
    private void OnPreviewSentenceStarted(SentenceDisplayData sentence)
    {
        // Atualiza navegação baseada na sentença atual
        UpdateNavigationFromSentence(sentence);
        Repaint(); // Atualiza a UI
    }

    private void OnPreviewSentenceFinished(SentenceDisplayData sentence)
    {
        Repaint(); // Atualiza a UI
    }

    private void OnPreviewDisplayFinished()
    {
        Debug.Log("Preview finalizado!");
        Repaint(); // Atualiza a UI
    }

    private void UpdateNavigationFromSentence(SentenceDisplayData sentence)
    {
        // Tenta encontrar a sentença atual nas frases
        for (int phraseIndex = 0; phraseIndex < _document.Phrases.Count; phraseIndex++)
        {
            var phrase = _document.Phrases[phraseIndex];
            for (int sentenceIndex = 0; sentenceIndex < phrase.Sentences.Count; sentenceIndex++)
            {
                if (phrase.Sentences[sentenceIndex] == sentence)
                {
                    _currentPreviewPhrase = phraseIndex;
                    _currentPreviewSentence = sentenceIndex;
                    return;
                }
            }
        }
    }

    private void DestroyPreviewObject()
    {
        if (_previewDisplayer != null) _previewDisplayer.StopDisplay();
        if (_previewObject != null)
        {
            DestroyImmediate(_previewObject);
            _previewObject = null;
            _previewDisplayer = null;
            _previewTextComponent = null;
        }
    }

    private void DrawSettingsTab()
    {
        EditorGUILayout.LabelField("⚙️ Editor Settings", EditorStyles.largeLabel);
        GUILayout.Space(10);

        // Editor preferences
        EditorGUILayout.LabelField("Editor Preferences", EditorStyles.boldLabel);
        _autoSave = EditorGUILayout.Toggle("Auto Save", _autoSave);

        GUILayout.Space(10);

        // Document settings (if document is selected)
        if (_document != null)
        {
            EditorGUILayout.LabelField("Document Settings", EditorStyles.boldLabel);

            // All default settings in a more organized way
            EditorGUILayout.LabelField("Default Visual Settings", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultTextColor"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultFontAsset"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultFontSize"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultIsBold"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultIsItalic"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultAlignment"));
            EditorGUI.indentLevel--;

            GUILayout.Space(5);

            EditorGUILayout.LabelField("Default Timing Settings", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultDelayAfterSentence"));
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultDelayAfterPhrase"));
            EditorGUI.indentLevel--;

            GUILayout.Space(5);

            EditorGUILayout.LabelField("Default Typewriter Settings", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultUseTypewriter"));
            if(_document.DefaultUseTypewriter)
            {
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultDelayPerCharacter"));
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultPauseOnPunctuation"));
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultPunctuationDelay"));
                EditorGUILayout.PropertyField(_serializedDocument.FindProperty("DefaultTypewriterSound"));
            }
            EditorGUI.indentLevel--;
        }
    }

    private void DrawStatisticsTab()
    {
        EditorGUILayout.LabelField("📊 Document Statistics", EditorStyles.largeLabel);
        GUILayout.Space(10);

        if (_document == null)
        {
            EditorGUILayout.HelpBox("No document selected.", MessageType.Info);
            return;
        }

        // Basic stats
        EditorGUILayout.LabelField("Basic Statistics", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;

        int totalSentences = GetTotalSentenceCount();
        int totalWords = GetTotalWordCount();
        int totalCharacters = GetTotalCharacterCount();

        EditorGUILayout.LabelField($"Phrases: {_document.Phrases.Count}");
        EditorGUILayout.LabelField($"Sentences: {totalSentences}");
        EditorGUILayout.LabelField($"Words: {totalWords}");
        EditorGUILayout.LabelField($"Characters: {totalCharacters}");

        EditorGUI.indentLevel--;
        GUILayout.Space(10);

        // Detailed phrase statistics
        EditorGUILayout.LabelField("Phrase Details", EditorStyles.boldLabel);

        for (int i = 0; i < _document.Phrases.Count; i++)
        {
            var phrase = _document.Phrases[i];
            int phraseWords = GetPhraseWordCount(phrase);
            int phraseChars = GetPhraseCharacterCount(phrase);

            EditorGUILayout.LabelField($"Phrase {i + 1}: {phrase.Sentences.Count} sentences, {phraseWords} words, {phraseChars} chars");
        }

        GUILayout.Space(10);

        // Typewriter timing estimation
        EditorGUILayout.LabelField("Timing Estimates", EditorStyles.boldLabel);
        EditorGUI.indentLevel++;

        float estimatedTime = CalculateEstimatedDisplayTime();
        EditorGUILayout.LabelField($"Estimated display time: {estimatedTime:F1} seconds");
        EditorGUILayout.LabelField($"Estimated reading time: {totalWords / 200f:F1} minutes (200 WPM)");

        EditorGUI.indentLevel--;
    }

    private void CreateNewDocument()
    {
        string path = EditorUtility.SaveFilePanelInProject(
            "Create New Visual Text Document",
            "NewTextDocument",
            "asset",
            "Choose where to save the new document");

        if (!string.IsNullOrEmpty(path))
        {
            VisualTextDocument newDoc = ScriptableObject.CreateInstance<VisualTextDocument>();
            newDoc.fullText = "Enter your text here...";

            AssetDatabase.CreateAsset(newDoc, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            SetDocument(newDoc);
            Selection.activeObject = newDoc;
        }
    }

    private int GetTotalWordCount()
    {
        if (_document == null) return 0;
        return _document.Phrases.Sum(p => p.Sentences.Sum(s =>
            string.IsNullOrEmpty(s.Text) ? 0 : s.Text.Split(' ', System.StringSplitOptions.RemoveEmptyEntries).Length));
    }

    private int GetTotalCharacterCount()
    {
        if (_document == null) return 0;
        return _document.Phrases.Sum(p => p.Sentences.Sum(s => s.Text?.Length ?? 0));
    }

    private int GetPhraseWordCount(PhraseData phrase)
    {
        return phrase.Sentences.Sum(s =>
            string.IsNullOrEmpty(s.Text) ? 0 : s.Text.Split(' ', System.StringSplitOptions.RemoveEmptyEntries).Length);
    }

    private int GetPhraseCharacterCount(PhraseData phrase)
    {
        return phrase.Sentences.Sum(s => s.Text?.Length ?? 0);
    }

    private float CalculateEstimatedDisplayTime()
    {
        if (_document == null) return 0f;

        float totalTime = 0f;

        foreach (var phrase in _document.Phrases)
        {
            foreach (var sentence in phrase.Sentences)
            {
                if (sentence.UseTypewriter)
                {
                    totalTime += sentence.Text?.Length * sentence.DelayPerCharacter ?? 0f;
                }
                totalTime += sentence.DelayAfterSentence;
            }
            totalTime += phrase.DelayAfterPhrase;
        }

        return totalTime;
    }

    private string Truncate(string value, int maxLength)
    {
        if (string.IsNullOrEmpty(value)) return value;
        return value.Length <= maxLength ? value : value.Substring(0, maxLength) + "...";
    }
}

